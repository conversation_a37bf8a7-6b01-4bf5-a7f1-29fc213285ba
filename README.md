# codegen - 配置驱动的 Swing 系统代码生成器

用途：连接到数据库读取“界面配置”，并按照模板生成一个全新的、可独立运行的 Swing 系统（新的 Maven 工程）。

## 快速使用
示例：
```
java -jar codegen-0.1.0-SNAPSHOT-shaded.jar \
  db.url=***************************************************************** \
  db.user=root db.pass=你的密码 \
  out.dir=D:/tmp/generated-app \
  groupId=com.acme artifactId=my-swing-app appName="My App" package=com.acme.app
```
生成结果目录中包含：pom.xml、src/main/java、src/main/resources、README.md 等；可直接用 Maven 打包运行。

## 模板结构
- templates/pom.xml.ftl
- templates/Main.java.ftl
- templates/App.java.ftl
- templates/UIBuilder.java.ftl（可根据 model 详细展开具体控件）

## 数据模型
从数据库读取的 ui_view 与 ui_component 组装为 ConfigModel，模板中可通过 ${model} 访问。

## TODO（可按需增强）
- 生成 DAO、服务层骨架、事件绑定
- 生成打包脚本、Dockerfile、安装包脚本
- 生成更丰富的 UI 布局和多视图切换
- 增加 Gradle 模板支持

