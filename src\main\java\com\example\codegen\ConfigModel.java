package com.example.codegen;

import java.util.ArrayList;
import java.util.List;

public class ConfigModel {
    public List<View> views = new ArrayList<>();

    public static class View {
        public long id; public String name; public String layout;
        public List<Component> components = new ArrayList<>();
    }
    public static class Component {
        public long id; public String type; public String label; public String name; public String optionsJson; public String constraints;
    }
}

