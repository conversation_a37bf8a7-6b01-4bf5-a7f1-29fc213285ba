package com.example.codegen;

import java.sql.*;

public class ConfigReader {
    private final Connection conn;
    public ConfigReader(Connection conn) { this.conn = conn; }

    public ConfigModel read() throws SQLException {
        ConfigModel model = new ConfigModel();
        try (Statement st = conn.createStatement(); ResultSet rs = st.executeQuery("select id,name,layout from ui_view order by id")) {
            while (rs.next()) {
                ConfigModel.View v = new ConfigModel.View();
                v.id = rs.getLong("id"); v.name = rs.getString("name"); v.layout = rs.getString("layout");
                model.views.add(v);
            }
        }
        for (ConfigModel.View v : model.views) {
            try (PreparedStatement ps = conn.prepareStatement("select id,type,label,name,options_json,constraints from ui_component where view_id=? order by id")) {
                ps.setLong(1, v.id);
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        ConfigModel.Component c = new ConfigModel.Component();
                        c.id = rs.getLong("id"); c.type = rs.getString("type"); c.label = rs.getString("label"); c.name = rs.getString("name"); c.optionsJson = rs.getString("options_json"); c.constraints = rs.getString("constraints");
                        v.components.add(c);
                    }
                }
            }
        }
        return model;
    }
}

