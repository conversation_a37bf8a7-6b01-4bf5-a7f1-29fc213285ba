package ${pkg};

import javax.swing.*;
import java.awt.*;

public class UIBuilder {
    public static JComponent build() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.gridx=0; gbc.gridy=0; gbc.insets=new Insets(4,4,4,4); gbc.anchor=GridBagConstraints.WEST; gbc.fill=GridBagConstraints.HORIZONTAL; gbc.weightx=1;

        // 根据配置生成的控件（简化示例）
        // 你可以继续在模板中根据 ${model} 详细展开

        panel.add(new JLabel("Generated UI"), gbc);
        gbc.gridy++;
        panel.add(new JTextField(20), gbc);
        gbc.gridy++;
        panel.add(new JButton("OK"), gbc);
        return panel;
    }
}

