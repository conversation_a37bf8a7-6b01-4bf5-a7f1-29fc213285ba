package com.example.codegen;

import java.nio.file.Path;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class CodegenMain {
    public static void main(String[] args) throws Exception {
        Map<String, String> opts = parseArgs(args);
        String url = require(opts, "db.url");
        String user = require(opts, "db.user");
        String pass = opts.getOrDefault("db.pass", "");
        String outDir = require(opts, "out.dir");
        String groupId = opts.getOrDefault("groupId", "com.generated");
        String artifactId = opts.getOrDefault("artifactId", "generated-app");
        String appName = opts.getOrDefault("appName", "Generated Swing App");
        String pkg = opts.getOrDefault("package", groupId + "." + artifactId.replace('-', '_'));

        try (Connection conn = DriverManager.getConnection(url, user, pass)) {
            // 1) 读取配置数据模型
            ConfigModel model = new ConfigReader(conn).read();
            // 2) 生成工程代码
            ProjectGenerator generator = new ProjectGenerator(groupId, artifactId, appName, pkg, Path.of(outDir));
            generator.generate(model);
            System.out.println("代码已生成到: " + outDir);
        }
    }

    private static String require(Map<String, String> m, String k) {
        String v = m.get(k);
        if (v == null || v.isBlank()) throw new IllegalArgumentException("缺少参数: " + k);
        return v;
    }

    private static Map<String, String> parseArgs(String[] args) {
        Map<String, String> m = new HashMap<>();
        for (String a : args) {
            int i = a.indexOf('=');
            if (i > 0) m.put(a.substring(0,i), a.substring(i+1));
        }
        return m;
    }
}

