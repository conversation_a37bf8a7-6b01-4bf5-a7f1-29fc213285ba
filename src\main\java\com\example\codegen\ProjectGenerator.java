package com.example.codegen;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.Version;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.io.Writer;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

public class ProjectGenerator {
    private final String groupId; private final String artifactId; private final String appName; private final String pkg; private final Path outDir;
    private final Configuration ftl;

    public ProjectGenerator(String groupId, String artifactId, String appName, String pkg, Path outDir) throws IOException {
        this.groupId = groupId; this.artifactId = artifactId; this.appName = appName; this.pkg = pkg; this.outDir = outDir;
        this.ftl = new Configuration(new Version("2.3.32"));
        this.ftl.setClassLoaderForTemplateLoading(getClass().getClassLoader(), "/templates");
        this.ftl.setDefaultEncoding("UTF-8");
    }

    public void generate(ConfigModel model) {
        try {
            Files.createDirectories(outDir);
            // 基础结构
            Path srcMainJava = outDir.resolve("src/main/java/" + pkg.replace('.', '/'));
            Path srcMainRes = outDir.resolve("src/main/resources");
            Files.createDirectories(srcMainJava);
            Files.createDirectories(srcMainRes);

            // pom.xml
            apply("pom.xml.ftl", map("groupId", groupId, "artifactId", artifactId, "appName", appName, "pkg", pkg), outDir.resolve("pom.xml"));
            // Main/App/UIBuilder
            apply("Main.java.ftl", map("pkg", pkg), srcMainJava.resolve("Main.java"));
            apply("App.java.ftl", map("pkg", pkg), srcMainJava.resolve("App.java"));
            apply("UIBuilder.java.ftl", map("pkg", pkg, "model", model), srcMainJava.resolve("UIBuilder.java"));
            // README
            apply("README.md.ftl", map("artifactId", artifactId), outDir.resolve("README.md"));
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    private Map<String, Object> map(Object... kv) {
        Map<String, Object> m = new HashMap<>();
        for (int i=0; i<kv.length; i+=2) m.put((String)kv[i], kv[i+1]);
        return m;
    }

    private void apply(String templateName, Map<String, Object> data, Path target) {
        try (Writer w = Files.newBufferedWriter(target)) {
            Template t = ftl.getTemplate(templateName);
            t.process(data, w);
        } catch (Exception e) {
            throw new RuntimeException("渲染模板失败: " + templateName, e);
        }
    }
}

